import { Injectable, Logger } from '@nestjs/common';
import { MailerService as NestMailerService } from '@nestjs-modules/mailer';
import { TemplateService } from './template.service';

@Injectable()
export class MailService {
  private readonly logger = new Logger(MailService.name);

  constructor(
    private readonly mailerService: NestMailerService,
    private readonly templateService: TemplateService,
  ) {}

  async sendResetPasswordMail(to: string, name: string, resetUrl: string) {
    try {
      const { subject, html } = this.templateService.getEmailTemplate('reset-password', {
        name,
        resetUrl,
      });

      await this.mailerService.sendMail({
        to,
        subject,
        html,
      });

      this.logger.log(`Reset password email sent to: ${to}`);
    } catch (error) {
      this.logger.error(`Failed to send reset password email to ${to}:`, error);
      throw new Error('Failed to send reset password email');
    }
  }

  async requestVerification(email: string, verificationUrl: string) {
    try {
      const { subject, html } = this.templateService.getEmailTemplate('verify-domain', {
        verificationUrl,
      });

      await this.mailerService.sendMail({
        to: email,
        subject,
        html,
      });

      this.logger.log(`Domain verification email sent to: ${email}`);
      return { message: 'Verification email sent' };
    } catch (error) {
      this.logger.error(`Failed to send verification email to ${email}:`, error);
      throw new Error('Failed to send verification email');
    }
  }

  async sendWelcomeEmail(to: string, name: string) {
    try {
      const { subject, html } = this.templateService.getEmailTemplate('welcome', {
        name,
      });

      await this.mailerService.sendMail({
        to,
        subject,
        html,
      });

      this.logger.log(`Welcome email sent to: ${to}`);
    } catch (error) {
      this.logger.error(`Failed to send welcome email to ${to}:`, error);
      throw new Error('Failed to send welcome email');
    }
  }

  async sendJobNotification(to: string, jobTitle: string, companyName: string, jobUrl: string) {
    try {
      const { subject, html } = this.templateService.getEmailTemplate('job-notification', {
        jobTitle,
        companyName,
        jobUrl,
      });

      await this.mailerService.sendMail({
        to,
        subject,
        html,
      });

      this.logger.log(`Job notification email sent to: ${to}`);
    } catch (error) {
      this.logger.error(`Failed to send job notification email to ${to}:`, error);
      throw new Error('Failed to send job notification email');
    }
  }
}
