import { Injectable, Logger } from '@nestjs/common';
import * as fs from 'fs';
import * as path from 'path';
import { ConfigService } from '@nestjs/config';

export interface TemplateVariables {
  [key: string]: string | number | boolean;
}

export interface EmailTemplate {
  subject: string;
  html: string;
}

@Injectable()
export class TemplateService {
  private readonly logger = new Logger(TemplateService.name);
  private readonly templateCache = new Map<string, string>();
  private readonly templatePaths: string[] = [];

  constructor(private readonly configService: ConfigService) {
    this.initializeTemplatePaths();
  }

  private initializeTemplatePaths(): void {
    // Define possible template locations in order of preference
    this.templatePaths = [
      // Production path (compiled)
      path.join(__dirname, 'templates'),
      // Development path from dist
      path.join(__dirname, '..', '..', '..', 'src', 'modules', 'mailer', 'templates'),
      // Direct path from project root
      path.join(process.cwd(), 'src', 'modules', 'mailer', 'templates'),
      // Fallback to current directory
      path.join(__dirname),
      // Legacy location
      path.join(__dirname, '..', '..', '..', 'src', 'modules', 'mailer'),
    ];

    this.logger.log('Template paths initialized:', this.templatePaths);
  }

  /**
   * Find template file in multiple possible locations
   */
  private findTemplateFile(templateName: string): string | null {
    const possibleExtensions = ['.html', '.hbs', '.ejs'];
    
    for (const basePath of this.templatePaths) {
      for (const ext of possibleExtensions) {
        const fullPath = path.join(basePath, `${templateName}${ext}`);
        if (fs.existsSync(fullPath)) {
          this.logger.log(`Template found: ${fullPath}`);
          return fullPath;
        }
      }
    }

    this.logger.warn(`Template not found: ${templateName}`);
    return null;
  }

  /**
   * Load template from file with caching
   */
  private loadTemplate(templateName: string): string {
    // Check cache first
    if (this.templateCache.has(templateName)) {
      return this.templateCache.get(templateName)!;
    }

    const templatePath = this.findTemplateFile(templateName);
    
    if (templatePath) {
      try {
        const content = fs.readFileSync(templatePath, 'utf8');
        // Cache the template
        this.templateCache.set(templateName, content);
        return content;
      } catch (error) {
        this.logger.error(`Error reading template ${templateName}:`, error);
      }
    }

    // Return fallback template
    return this.getFallbackTemplate(templateName);
  }

  /**
   * Get fallback template when file is not found
   */
  private getFallbackTemplate(templateName: string): string {
    const fallbackTemplates: Record<string, string> = {
      'verify-domain': `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="UTF-8">
          <title>Verify Your Domain</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }
            .container { max-width: 600px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            .header { text-align: center; margin-bottom: 30px; }
            .logo { font-size: 24px; font-weight: bold; color: #1976F6; }
            .button { display: inline-block; background: #1976F6; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 20px 0; font-weight: bold; }
            .footer { margin-top: 30px; font-size: 12px; color: #666; text-align: center; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <div class="logo">TalentLoop</div>
            </div>
            <h2>Verify Your Domain</h2>
            <p>Hello,</p>
            <p>To complete your registration, please verify your domain by clicking the button below:</p>
            <a href="{{verificationUrl}}" class="button">Verify Domain</a>
            <p>If you did not request this, you can safely ignore this email.</p>
            <p>This link will expire in 15 minutes for security reasons.</p>
            <div class="footer">
              &copy; {{year}} TalentLoop. All rights reserved.
            </div>
          </div>
        </body>
        </html>
      `,
      'reset-password': `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="UTF-8">
          <title>Reset Your Password</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }
            .container { max-width: 600px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            .header { text-align: center; margin-bottom: 30px; }
            .logo { font-size: 24px; font-weight: bold; color: #1976F6; }
            .button { display: inline-block; background: #1976F6; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 20px 0; font-weight: bold; }
            .footer { margin-top: 30px; font-size: 12px; color: #666; text-align: center; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <div class="logo">TalentLoop</div>
            </div>
            <h2>Reset Your Password</h2>
            <p>Hello {{name}},</p>
            <p>You requested to reset your password. Click the button below to set a new password:</p>
            <a href="{{resetUrl}}" class="button">Reset Password</a>
            <p>If you did not request this, you can safely ignore this email.</p>
            <p>This link will expire in 15 minutes for security reasons.</p>
            <div class="footer">
              &copy; {{year}} TalentLoop. All rights reserved.
            </div>
          </div>
        </body>
        </html>
      `
    };

    const fallback = fallbackTemplates[templateName];
    if (fallback) {
      this.logger.warn(`Using fallback template for: ${templateName}`);
      return fallback;
    }

    // Ultimate fallback
    return `
      <!DOCTYPE html>
      <html>
      <head><title>{{subject}}</title></head>
      <body>
        <h2>{{subject}}</h2>
        <p>{{message}}</p>
        <p>&copy; {{year}} TalentLoop. All rights reserved.</p>
      </body>
      </html>
    `;
  }

  /**
   * Process template with variables
   */
  private processTemplate(template: string, variables: TemplateVariables): string {
    let processed = template;

    // Replace all variables
    Object.entries(variables).forEach(([key, value]) => {
      const regex = new RegExp(`{{${key}}}`, 'g');
      processed = processed.replace(regex, String(value));
    });

    // Add default variables
    const defaultVariables = {
      year: new Date().getFullYear(),
      appName: 'TalentLoop',
      supportEmail: this.configService.get('mail.supportEmail', '<EMAIL>'),
    };

    Object.entries(defaultVariables).forEach(([key, value]) => {
      const regex = new RegExp(`{{${key}}}`, 'g');
      processed = processed.replace(regex, String(value));
    });

    return processed;
  }

  /**
   * Get processed email template
   */
  getEmailTemplate(templateName: string, variables: TemplateVariables = {}): EmailTemplate {
    try {
      const template = this.loadTemplate(templateName);
      const html = this.processTemplate(template, variables);

      // Extract subject from template or use default
      const subjectMatch = template.match(/<title>(.*?)<\/title>/i);
      const subject = subjectMatch ? subjectMatch[1] : this.getDefaultSubject(templateName);

      return {
        subject: this.processTemplate(subject, variables),
        html
      };
    } catch (error) {
      this.logger.error(`Error processing template ${templateName}:`, error);
      
      // Return emergency fallback
      return {
        subject: 'Important Message from TalentLoop',
        html: this.getFallbackTemplate('emergency')
      };
    }
  }

  /**
   * Get default subject for template
   */
  private getDefaultSubject(templateName: string): string {
    const subjects: Record<string, string> = {
      'verify-domain': 'Verify Your Domain - TalentLoop',
      'reset-password': 'Reset Your Password - TalentLoop',
      'welcome': 'Welcome to TalentLoop',
      'job-notification': 'New Job Opportunity - TalentLoop'
    };

    return subjects[templateName] || 'Message from TalentLoop';
  }

  /**
   * Clear template cache (useful for development)
   */
  clearCache(): void {
    this.templateCache.clear();
    this.logger.log('Template cache cleared');
  }

  /**
   * Preload all templates (useful for production)
   */
  preloadTemplates(): void {
    const templateNames = ['verify-domain', 'reset-password'];
    
    templateNames.forEach(name => {
      this.loadTemplate(name);
    });

    this.logger.log(`Preloaded ${templateNames.length} templates`);
  }
}
