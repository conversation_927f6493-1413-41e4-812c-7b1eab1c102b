<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Reset Your Password - TalentLoop</title>
  <style>
    body {
      margin: 0;
      padding: 0;
      font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: #333;
    }
    .email-container {
      max-width: 600px;
      margin: 40px auto;
      background: #ffffff;
      border-radius: 16px;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
      overflow: hidden;
    }
    .header {
      background: linear-gradient(135deg, #1976F6 0%, #1565C0 100%);
      padding: 40px 30px;
      text-align: center;
      color: white;
    }
    .logo {
      font-size: 28px;
      font-weight: bold;
      margin-bottom: 10px;
    }
    .header-subtitle {
      font-size: 16px;
      opacity: 0.9;
    }
    .content {
      padding: 40px 30px;
    }
    .greeting {
      font-size: 18px;
      margin-bottom: 20px;
      color: #333;
    }
    .message {
      font-size: 16px;
      line-height: 1.6;
      color: #555;
      margin-bottom: 30px;
    }
    .cta-button {
      display: inline-block;
      background: linear-gradient(135deg, #1976F6 0%, #1565C0 100%);
      color: white;
      padding: 16px 32px;
      text-decoration: none;
      border-radius: 8px;
      font-weight: bold;
      font-size: 16px;
      text-align: center;
      box-shadow: 0 4px 15px rgba(25, 118, 246, 0.3);
      transition: transform 0.2s ease;
    }
    .cta-button:hover {
      transform: translateY(-2px);
    }
    .security-note {
      background: #fff3cd;
      border-left: 4px solid #ffc107;
      padding: 15px 20px;
      margin: 30px 0;
      border-radius: 4px;
    }
    .security-note h4 {
      margin: 0 0 10px 0;
      color: #856404;
      font-size: 14px;
    }
    .security-note p {
      margin: 0;
      font-size: 14px;
      color: #856404;
    }
    .footer {
      background: #f8f9fa;
      padding: 30px;
      text-align: center;
      border-top: 1px solid #e9ecef;
    }
    .footer-text {
      font-size: 14px;
      color: #666;
      margin-bottom: 15px;
    }
    .social-links {
      margin: 20px 0;
    }
    .social-links a {
      display: inline-block;
      margin: 0 10px;
      color: #1976F6;
      text-decoration: none;
    }
    .copyright {
      font-size: 12px;
      color: #999;
    }
    @media (max-width: 600px) {
      .email-container {
        margin: 20px;
        border-radius: 12px;
      }
      .header, .content, .footer {
        padding: 30px 20px;
      }
      .cta-button {
        display: block;
        margin: 20px 0;
      }
    }
  </style>
</head>
<body>
  <div class="email-container">
    <div class="header">
      <div class="logo">TalentLoop</div>
      <div class="header-subtitle">Professional Talent Platform</div>
    </div>
    
    <div class="content">
      <div class="greeting">Hello {{name}}! 👋</div>
      
      <div class="message">
        <p>We received a request to reset your password for your TalentLoop account.</p>
        
        <p>If you made this request, click the button below to set a new password. If you didn't request a password reset, you can safely ignore this email.</p>
      </div>
      
      <div style="text-align: center; margin: 40px 0;">
        <a href="{{resetUrl}}" class="cta-button">Reset My Password</a>
      </div>
      
      <div class="security-note">
        <h4>⚠️ Security Notice</h4>
        <p>This password reset link will expire in <strong>15 minutes</strong> for your security. If you need to reset your password after this time, please request a new reset link.</p>
      </div>
      
      <div class="message">
        <p>If the button above doesn't work, you can copy and paste this link into your browser:</p>
        <p style="word-break: break-all; color: #1976F6; font-family: monospace; background: #f8f9fa; padding: 10px; border-radius: 4px;">{{resetUrl}}</p>
      </div>
      
      <div class="message">
        <p><strong>Didn't request this?</strong> If you didn't request a password reset, please contact our support team immediately at <a href="mailto:{{supportEmail}}" style="color: #1976F6;">{{supportEmail}}</a></p>
      </div>
    </div>
    
    <div class="footer">
      <div class="footer-text">
        Need help? Contact our support team at <a href="mailto:{{supportEmail}}" style="color: #1976F6;">{{supportEmail}}</a>
      </div>
      
      <div class="social-links">
        <a href="#">LinkedIn</a> |
        <a href="#">Twitter</a> |
        <a href="#">Website</a>
      </div>
      
      <div class="copyright">
        &copy; {{year}} {{appName}}. All rights reserved.<br>
        This email was sent in response to your password reset request.
      </div>
    </div>
  </div>
</body>
</html>
